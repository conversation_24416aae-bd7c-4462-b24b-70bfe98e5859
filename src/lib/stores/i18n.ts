// src/stores/i18n.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store ------------ */
const stored = browser ? (localStorage.getItem('lang') as 'en' | 'th' | null) : null;
export const language = writable<'en' | 'th'>(stored ?? 'en');

if (browser) {
	language.subscribe((v) => localStorage.setItem('lang', v));
}

/* ------------ active dictionary ------------ */
export const dict = derived(language, ($l) => dictionaries[$l] ?? dictionaries.en);

/* ------------ helper ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}

/**
 * Parse date string with support for multiple formats
 * @param {string} dateStr - Date string to parse
 * @returns {Date | null} Parsed Date object or null if invalid
 */
function parseDateString(dateStr: string): Date | null {
	if (!dateStr || typeof dateStr !== 'string') return null;

	// Check for DD/MM/YYYY format (e.g., "31/10/2024")
	const ddmmyyyyPattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
	const ddmmyyyyMatch = dateStr.trim().match(ddmmyyyyPattern);

	if (ddmmyyyyMatch) {
		const day = parseInt(ddmmyyyyMatch[1], 10);
		const month = parseInt(ddmmyyyyMatch[2], 10);
		const year = parseInt(ddmmyyyyMatch[3], 10);

		// Validate ranges
		if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1000 || year > 9999) {
			return null; // Invalid ranges
		}

		// Create date object (month is 0-indexed in JavaScript Date)
		const dateObj = new Date(year, month - 1, day);

		// Verify the date is valid (handles cases like 31/02/2024)
		if (dateObj.getFullYear() !== year ||
			dateObj.getMonth() !== month - 1 ||
			dateObj.getDate() !== day) {
			return null; // Invalid date (e.g., Feb 31st)
		}

		return dateObj;
	}

	// Fall back to standard Date parsing for other formats (ISO strings, etc.)
	const standardDate = new Date(dateStr);
	return isNaN(standardDate.getTime()) ? null : standardDate;
}

/**
 * Format date based on language
 * @param {string|Date} date - Date to format (supports ISO strings, DD/MM/YYYY format, and Date objects)
 * @param {string} lang - Language code (optional, defaults to current language from store)
 * @returns {string} Formatted date
 */
export function formatDateByLanguage(date: string | Date, lang?: 'en' | 'th'): string {
	const currentLanguage = lang ?? get(language);
	if (!date) return currentLanguage === 'en' ? 'N/A' : 'ไม่มีข้อมูล';

	try {
		let dateObj: Date;

		if (typeof date === 'string') {
			const parsed = parseDateString(date);
			if (!parsed) {
				return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
			}
			dateObj = parsed;
		} else {
			dateObj = date;
		}

		if (isNaN(dateObj.getTime())) {
			return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
		}

		if (currentLanguage === 'en') {
			// English format: Month DD, YYYY
			const options: Intl.DateTimeFormatOptions = {
				year: 'numeric',
				month: 'long',
				day: 'numeric'
			};
			return dateObj.toLocaleDateString('en-US', options);
		} else {
			// Thai format with Buddhist Era (BE)
			const day = dateObj.getDate().toString();
			const monthName = dateObj.toLocaleDateString('th-TH', { month: 'long' });
			const yearBE = dateObj.getFullYear() + 543;
			return `${day} ${monthName} ${yearBE}`;
		}
	} catch (error) {
		console.warn('Error formatting date:', error);
		return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
	}
}

/**
 * Format short date based on language
 * @param {string|Date} date - Date to format (supports ISO strings, DD/MM/YYYY format, and Date objects)
 * @param {string} lang - Language code (optional, defaults to current language from store)
 * @returns {string} Formatted short date
 */
export function formatShortDateByLanguage(date: string | Date, lang?: 'en' | 'th'): string {
	const currentLanguage = lang ?? get(language);
	if (!date) return currentLanguage === 'en' ? 'N/A' : 'ไม่มีข้อมูล';

	try {
		let dateObj: Date;

		if (typeof date === 'string') {
			const parsed = parseDateString(date);
			if (!parsed) {
				return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
			}
			dateObj = parsed;
		} else {
			dateObj = date;
		}

		if (isNaN(dateObj.getTime())) {
			return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
		}

		if (currentLanguage === 'en') {
			// English format: MMM DD, YYYY
			const options: Intl.DateTimeFormatOptions = {
				year: 'numeric',
				month: 'short',
				day: 'numeric'
			};
			return dateObj.toLocaleDateString('en-US', options);
		} else {
			// Thai format with Buddhist Era (BE)
			const day = dateObj.getDate().toString();
			const monthName = dateObj.toLocaleDateString('th-TH', { month: 'short' });
			const yearBE = dateObj.getFullYear() + 543;
			return `${day} ${monthName} ${yearBE}`;
		}
	} catch (error) {
		console.warn('Error formatting short date:', error);
		return currentLanguage === 'en' ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
	}
}